<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form :form="form" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-item label="资料名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['name', validatorRules.name]"
                       placeholder="请输入资料名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['type', validatorRules.type]"
                :trigger-change="true"
                dictCode="training_material_type"
                placeholder="请选择类型">
              </j-dict-select-tag>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上传人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['username',validatorRules.username]" placeholder="请输入上传人"
                       disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上传单位" :labelCol="labelCol" :wrapperCol="wrapperCol" disabled>
              <a-input autocomplete="off" v-decorator="['unitName',validatorRules.unitName]"
                       placeholder="请输入上传单位" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="封面图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload text="封面图片" :number="1" :fileType="'image'" v-decorator="['cover']" size="'mine'">
                <template slot="icon">
                  <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
                </template>
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="资料附件" :labelCol="labelCol" :wrapperCol="wrapperCol">

              <j-upload text="资料附件" :number="1" :fileType="'all'" v-decorator="['file']" size="'mine'">
                <template slot="icon">
                  <a-icon type="plus-circle" theme="filled" :style="{ fontSize: '24px', color: '#0096ff' }" />
                </template>
              </j-upload>
            </a-form-item>
          </a-col>
          <a-col v-if="showFlowSubmitButton" :span="24" style="text-align: center">
            <a-button @click="submitForm">提 交</a-button>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
  </a-spin>
</template>

<script>

import { httpAction, getAction } from '@api/manage'
import pick from 'lodash.pick'
import { mapGetters } from 'vuex'
import { getStore } from '@/utils/storage'

export default {
  name: 'TrainingMaterialForm',
  components: {},
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：true流程表单 false普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      unit: '',
      username: '',
      userId: '',
      unitName: '',
      form: this.$form.createForm(this),
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      validatorRules: {
        name: {
          rules: [
            { required: true, message: '请输入资料名称!' }
          ]
        },
        username: {
          rules: [
            { required: true, message: '上传人不能为空' }
          ]
        },
        unitName: {
          rules: [
            { required: true, message: '上传单位不能为空' }
          ]
        },
        type: {
          rules: [
            { required: true, message: '类型不能为空' }
          ]
        }
      },
      url: {
        add: '/education/trainingMaterial/add',
        edit: '/safety/trainingMaterial/edit',
        queryById: '/safety/trainingMaterial/queryById',
        unitUrl: '/person/personInfo/queryByUsername'

      }
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    }
  },
  created() {
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  mounted() {
    // 获取当前登录人信息

  },
  methods: {
    add() {
      this.edit({})
    },
    edit(record) {
      this.username = this.userInfo.realname
      console.log("获取缓存人员信息",this.userInfo)
      this.unitName = this.userInfo.workDepartmentName
      this.unit = this.userInfo.workDepartmentId
      console.log(this.unitName,this.unit,this.userInfo.workDepartmentName)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      this.$nextTick(() => {
        // 设置表单字段值，不包括username和unitName，这两个在mounted中处理
        this.form.setFieldsValue(pick(this.model, 'name', 'violationType', 'cover', 'file', 'unit', 'type'))
        console.log('是否编辑模式', this.model.id, this.model.username, this.model.unitName)
        // 如果是编辑模式且有记录中的username和unitName，则设置它们
        if (this.model.id) {
          // 编辑模式下，显示记录中的上传人和单位
          this.form.setFieldsValue({
            username: this.model.username,
            unitName: this.model.unitName
          })
        } else {
          // 新增模式下，显示当前登录人信息
          this.form.setFieldsValue({
            username: this.username,
            unitName: this.unitName,
            unit: this.unit // 设置单位ID，这个会被提交
          })
        }
      })
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
            // 新增时设置单位ID
            values.unit = this.unit
          } else {
            delete values.username
            delete values.unitName
            delete values.unit
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'createBy', 'name', 'type', 'cover', 'file', 'unit', 'type'))
    }
  }
}
</script>