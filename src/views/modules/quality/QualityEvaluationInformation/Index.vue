<template>
  <div class="list-page project-process-list">
    <table-layout
      ref="tableLayout1"
      :table-props="tableProps"
      :rightTitle="rightTitle"
      :tree-props="treeProps"
      :search-props="searchProps"
      @tree-init="onTreeInit"
      @tree-select="handleTreeSelect"
      @table-change="handleSearchSubmit"
      @search-submit="handleSearchSubmit"
    >
      <!--      <template #icon='{ record }'>-->
      <!--        <img-->
      <!--          v-if='record.icon'-->
      <!--          :src='getImageUrl(record.icon)'-->
      <!--          style='width: 30px; height: 30px'-->
      <!--          @click='clickImage(record.icon)'-->
      <!--        />-->
      <!--      </template>-->
      <template #archiveStatus="{record}">
        <a-tag v-if="record.detail && record.detail.archiveStatus === 'completed'" color="green">已归集</a-tag>
        <a-tag v-else color="red">未归集</a-tag>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk" @submit="modalFormSubmit"></modal>
  </div>
</template>

<script>

import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import Modal from './components/Modal'
import { filterMultiDictText, getDictItems } from '@/components/dict/JDictSelectUtil'
import '@/assets/less/TableExpand.less'
import { deleteAction, getAction, postAction } from '@api/manage'

export default {
  name: 'QualityEvaluationInformationList',
  mixins: [JeecgListMixin],
  components: {
    Modal
  },
  data() {
    return {
      evallutionDict: {},
      flowCode: 'dev_quality_evaluation_information_001',
      searchProps: {
        formModel: {
          name: null,
          code: null,
          bmpStatus: null,
          reporter: null,
          supervisor: null
        },
        formItems: [
          { key: 'name', label: 'qbs名称', type: 'text' },
          { key: 'code', label: 'qbs编码', type: 'text' },
          { key: 'bpmStatus', label: '流程状态', type: 'list', dictCode: 'bpm_status' },
          { key: 'reporter', label: '检查上报人', type: 'text' },
          { key: 'supervisor', label: '监理审核人', type: 'text' }
        ]
      },
      dictOptions: {},
      superFieldList: [],
      previewImageUrl: '',
      visable: false,
      disableMixinCreated: true,
      isorter: {
        column: 'code',
        order: 'asc'
      },
      rightTitle: '验评资料管理页面',
      searchData: {},
      pid: '',
      qbsId: '', // 当前选中的树节点ID
      // 树组件的props
      treeProps: {
        treeField: 'pid',
        selectedKeys: [],
        isSelectParentNodes: true,
        replaceFields: { title: 'name', key: 'id' },
        treeData: [],
        internalTreeLoading: true,
        checkStrictly: true,
        defaultExpandParent: true,
        autoExpandParent: false
      },

      tableProps: {
        loading: false,
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0
        },
        dataSource: [],
        columns: [
          {
            title: '#',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function(t, r, index) {
              return parseInt(index) + 1
            }
          },
          {
            title: 'qbs名称',
            align: 'center',
            dataIndex: 'name'
          },
          {
            title: 'qbs编码',
            align: 'center',
            dataIndex: 'code'
          },
          {
            title: '验评结果',
            align: 'center',
            dataIndex: 'evaluationState',
            customRender: (text, record) => {
              if (!record.detail) {
                return ''
              }
              let state = record.detail.evaluationState
              let item = this.evallutionDict.find(item => item.value === state)
              return item ? item.text : ''
            }
          },
          {
            title: '验评日期',
            align: 'center',
            dataIndex: 'createTime'
          },
          {
            title: '流程状态',
            align: 'center',
            customRender: (text, record) => {
              if (!record.detail || !record.detail.bpmStatus) {
                return '待发起'
              } else {
                switch (record.detail.bpmStatus) {
                  case '1':
                    return '待提交'
                  case '2':
                    return '处理中'
                  case '3':
                    return '已完成'
                  case '4':
                    return '已作废'
                  case '5':
                    return '已挂起'
                  default:
                    return '待发起'
                }

              }
            }
          },
          {
            title: '当前执行人',
            align: 'center',
            customRender: (text, record) => {
              // 只有在监理审批环节及之后才显示监理审核人
              // 假设流程状态：1-发起，2-监理审批中，3-审批完成
              if (record.detail && (record.detail.bmpStatus === '2' || record.detail.bmpStatus === '3')) {
                return record.detail.supervisor || ''
              }
              return ''
            }
          },
          {
            title: '归集状态',
            align: 'center',
            scopedSlots: { customRender: 'archiveStatus' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' }
          }
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.detail && (record.detail.bpmStatus === '2' || record.detail.bpmStatus === '3')
            }
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.detail && (record.detail.bpmStatus === '2' || record.detail.bpmStatus === '3')
            }

          }
        ],
        headerButtons: [
          // {
          //   text: '新增',
          //   icon: 'plus-circle',
          //   handler: this.handleAdd
          // }
        ]
      },

      url: {
        rootList: '/quality/qualityCatalogueTree/rootList',
        childList: '/quality/qualityCatalogueTree/childList',
        list: '/quality/qualityCatalogueTree/childList',
        delete: '/quality/qualityEvaluationInformation/delete',
        deleteBatch: '/quality/qualityEvaluationInformation/deleteBatch',
        exportXlsUrl: '/quality/qualityEvaluationInformation/exportXls',
        importExcelUrl: 'quality/qualityEvaluationInformation/importExcel',
        startProcess: '/act/process/extActProcess/startMutilProcess'
      }
    }
  },

  created() {
    // this.getSuperFieldList()
    this.loadCustomTreeData()
    this.initDict()

  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDict() {
      getDictItems('evaluation_status').then((resp) => {

        this.evallutionDict = resp
        console.log('evallutionDict', this.evallutionDict)

      })
    },
    updateHeaderList(qbsLayer) {
      // 层级对应关系：当前层级 -> 其子级名称
      const childLevelMap = {
        'zgc': '单位工程',    // 子工程的子级是单位工程
        'dwgc': '分部工程',   // 单位工程的子级是分部工程
        'fbgc': '单元工程',   // 分部工程的子级是单元工程
        'dygc': '检验批'      // 单元工程的子级是检验批（或其他最小单位）
      }
      // 获取子级名称，如果没有对应关系则使用默认值
      const childLevelName = childLevelMap[qbsLayer] || 'qbs'
      // 更新表格列标题
      this.tableProps.columns[1].title = childLevelName + '名称'
      this.tableProps.columns[2].title = childLevelName + '编码'
      // 更新搜索表单项的标签
      this.searchProps.formItems[0].label = childLevelName + '名称'
      this.searchProps.formItems[1].label = childLevelName + '编码'
    },
    loadCustomTreeData() {
      getAction(this.url.rootList, { pid: '0' }).then((res) => {
        if (res.success) {
          // 处理数据 处理成树形结构
          this.treeProps.treeData = res.result
          // 注意：不在这里设置 pid，让 onTreeInit 事件来处理
        }
      })
    },
    handleAdd(record) {
      this.$refs.modalForm.title = '新增'
      this.$refs.modalForm.edit({
        pid: this.pid,
        weight: 0,
        isProgress: 0,
        warningEnable: 0,
        qbsName: record.name,
        qbsCode: record.code
      })
      this.$refs.modalForm.disableSubmit = false
    },
    handleDetail(record) {
      this.$refs.modalForm.title = '详情'
      let detail = {}
      detail = record.detail
      detail.qbsId = record.id
      console.log('detail', detail)
      this.$refs.modalForm.edit(detail)
      this.$refs.modalForm.disableSubmit = true
    },
    handleEdit(record) {
      console.log('handleEdit', record)
      this.$refs.modalForm.title = '编辑'
      // record['pid'] = this.pid
      let detail = {}
      if (!record.detail) {
        detail = {
          qbsId: record.id,
          qbsName: record.name,
          qbsCode: record.code
        }
      } else {
        detail = record.detail
        detail.qbsId = record.id
      }
      console.log('detail', detail)
      this.$refs.modalForm.edit(detail)
      this.$refs.modalForm.disableSubmit = false
    },
    handleDelete: function(id) {
      if (typeof id === 'object') {
        id = id.id
      }
      if (!this.url.delete) {
        this.$message.error('请设置url.delete属性!')
        return
      }
      let that = this
      this.$confirm({
        title: '提醒',
        content: '确认要删除吗?',
        okText: '确认',
        cancelText: '取消',
        onOk() {
          deleteAction(that.url.delete, { id: id }).then((res) => {
            if (res.success) {
              that.$message.success('删除成功！')
              that.queryParam = { ...that.queryParam, pid: that.getCurrentTreeId() }
              that.loadData()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {
        }
      })
    },

    modalFormOk() {
      this.queryParam = { ...this.queryParam, pid: this.pid }
      this.loadData()
    },
    modalFormSubmit(data) {
      console.log('modalFormSubmit', data)
      this.startProcess({ id: data })
    },
    startProcess: function(record) {
      var that = this
      var key = 'startProcess'
      this.$message.loading({ content: '正在提交流程...', key })
      var param = {
        flowCode: that.flowCode,
        id: record.id,
        formUrl: 'modules/quality/QualityEvaluationInformation/components/Form',
        formUrlMobile: ''
      }
      postAction(that.url.startProcess, param).then((res) => {
        if (res.success) {
          that.$message.success({ content: res.message + '!', key, duration: 1 })
          that.modalFormOk()
        } else {
          that.$message.warning({ content: res.message, key, duration: 1 })
        }
      })
    },

    handleSearchSubmit(params) {
      console.log('搜索提交', params)
      // 将搜索表单的数据合并到 queryParam 中
      if (params && params.formModel) {
        this.queryParam = { ...params.formModel, pid: this.pid }
      } else {
        this.queryParam = { pid: this.pid }
      }
      console.log('最终查询参数', this.queryParam)
      this.loadData(1) // 重置到第一页
    },
    handleTreeSelect({ selectedKeys, e }) {
      // 处理树节点选择事件
      console.log('树节点点击', selectedKeys, e)
      if (!selectedKeys || selectedKeys.length === 0) {
        return
      }
      // 通过 findNodeInTree 方法从树数据中查找节点
      const nodeData = this.findNodeInTree(this.treeProps.treeData, selectedKeys[0])
      console.log('节点数据:', nodeData)

      this.pid = selectedKeys[0]
      this.tableProps.ipagination.current = 1

      if (nodeData && nodeData.qbsLayer) {
        this.updateHeaderList(nodeData.qbsLayer)
      }

      // 更新 queryParam 中的 pid，保留其他搜索条件
      this.queryParam = { ...this.queryParam, pid: this.pid }
      this.loadData(1) // 重置到第一页
    },
    getCurrentTreeId() {
      let selectedKeys = this.$refs.tableLayout1.mergeParams.selectedKeys
      let treeId = selectedKeys ? selectedKeys[0] : undefined
      return treeId
    },
    onTreeInit(params) {
      console.log('树初始化事件', params)
      // 获取第一个选中的节点ID
      const firstSelectedKey = params.selectedKeys && params.selectedKeys.length > 0 ? params.selectedKeys[0] : null

      if (firstSelectedKey) {
        // 查找对应的节点数据
        const nodeData = this.findNodeInTree(this.treeProps.treeData, firstSelectedKey)
        console.log('初始化选中的节点数据:', nodeData)

        // 设置当前选中的pid
        this.pid = firstSelectedKey

        // 如果找到节点数据且有层级信息，更新标签
        if (nodeData && nodeData.qbsLayer) {
          this.updateHeaderList(nodeData.qbsLayer)
        }

        // 设置查询参数并加载对应的数据
        this.queryParam = { pid: this.pid }
        this.loadData(1)
      } else {
        // 如果没有选中节点，使用默认的第一个节点
        if (this.treeProps.treeData && this.treeProps.treeData.length > 0) {
          const firstNode = this.treeProps.treeData[0]
          this.pid = firstNode.id

          if (firstNode.qbsLayer) {
            this.updateHeaderList(firstNode.qbsLayer)
          }

          this.queryParam = { pid: this.pid }
          this.loadData(1)
        }
      }
    },

    // 辅助方法：从树数据中查找节点
    findNodeInTree(treeData, nodeId) {
      if (!treeData || !Array.isArray(treeData) || !nodeId) {
        return null
      }

      // 递归查找节点
      const findNode = (nodes) => {
        for (let node of nodes) {
          // 匹配节点ID（支持字符串和数字类型）
          if (String(node.id) === String(nodeId)) {
            return node
          }
          // 如果有子节点，递归查找
          if (node.children && Array.isArray(node.children) && node.children.length > 0) {
            const found = findNode(node.children)
            if (found) {
              return found
            }
          }
        }
        return null
      }

      return findNode(treeData)
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>