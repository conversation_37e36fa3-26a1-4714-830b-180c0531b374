<template>
  <div class="list-page quality-issue-rectifications-list">
    <table-layout
      :rightTitle="rightTitle"
      :search-props="searchProps"
      :table-props="tableProps"
      @init-params="initParams"
      @search-submit="searchQuery"
      @table-change="onTableChange"
    >
      <template slot="rectificationStatus" slot-scope="record">
        <span v-if="!record.record.rectificationDeadline">-</span>
        <div v-else>
          <a-tag :color="getDeadlineStatus(record.record) ? 'red' : 'green'">
            {{ getDeadlineStatus(record) ? '已逾期' : '未逾期' }}
          </a-tag>
        </div>
      </template>
    </table-layout>
    <modal ref="modalForm" @ok="modalFormOk" @submit="modalFormSubmit"></modal>
  </div>
</template>

<script>
import Modal from './components/Modal'
import { JeecgTreeListMixin } from '@/mixins/JeecgTreeListMixin'
import { BpmNodeInfoMixin } from '@/views/modules/bpm/mixins/BpmNodeInfoMixin'
import { postAction } from '@/api/manage'

export default {
  name: 'QualityIssueRectificationsList',
  mixins: [JeecgTreeListMixin, BpmNodeInfoMixin],
  components: {
    Modal,
  },
  data() {
    return {
      rightTitle: '质量整改列表',
      // 搜索组件的props
      searchProps: {
        formModel: {
          code: null,
          reportDate: null,
        },
        formItems: [
          { key: 'code', label: '整改单号', type: 'text' },
          { key: 'reportDate', label: '上报日期', type: 'date' },
        ],
      },
      // 表格组件的props
      tableProps: {
        loading: false,
        dataSource: [],
        ipagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100'],
          showTotal: (total) => {
            return ' 共' + total + '条'
          },
          showSizeChanger: true,
          total: 0,
        },
        columns: [
          {
            title: '序号',
            dataIndex: '',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: (t, r, index) => {
              return parseInt(index) + 1
            },
          },
          {
            title: '整改单号',
            align: 'center',
            dataIndex: 'code',
          },
          {
            title: '整改工区',
            align: 'center',
            dataIndex: 'workArea',
          },
          {
            title: '整改部位',
            align: 'center',
            dataIndex: 'rectificationPart_dictText',
          },
          {
            title: '整改内容',
            align: 'center',
            dataIndex: 'rectificationContent',
          },
          {
            title: '整改期限',
            align: 'center',
            dataIndex: 'rectificationDeadline',
          },
          {
            title: '上报日期',
            align: 'center',
            dataIndex: 'reportDate',
            customRender: (text) => {
              return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
            },
          },
          {
            title: '整改状态',
            align: 'center',
            dataIndex: 'rectificationStatus_dictText',
          },
          {
            title: '逾期状态',
            align: 'center',
            scopedSlots: { customRender: 'rectificationStatus' },
          },
          {
            title: '检查上报人',
            align: 'center',
            dataIndex: 'reporter',
          },

          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            fixed: 'right',
            width: 160,
            scopedSlots: { customRender: 'action' },
          },
        ],
        actionButtons: [
          {
            text: '查看',
            handler: this.handleDetail,
          },
          {
            text: '编辑',
            handler: this.handleEdit,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
          {
            text: '删除',
            type: 'danger',
            handler: this.handleDelete,
            disabled: (record) => {
              return record.bpmStatus == '2' || record.bpmStatus == '3'
            },
          },
        ],
        headerButtons: [
          //这里只能编辑已有项目
          {
            text: '新增',
            icon: 'plus',
            handler: this.handleAdd,
          },
        ],
      },
      flowCode: 'dev_quality_issue_rectifications_001',
      url: {
        list: '/quality/qualityIssueRectifications/list',
        delete: '/quality/qualityIssueRectifications/delete',
        deleteBatch: '/quality/qualityIssueRectifications/deleteBatch',
        exportXlsUrl: '/quality/qualityIssueRectifications/exportXls',
        importExcelUrl: 'quality/qualityIssueRectifications/importExcel',
        startProcess: '/act/process/extActProcess/startMutilProcess',
      },
    }
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    initParams(mergeParams) {
      this.queryParam = mergeParams.formModel
    },

    // 格式化日期显示
    formatDate(dateStr) {
      if (!dateStr) return ''
      return dateStr.length > 10 ? dateStr.substr(0, 10) : dateStr
    },

    // 获取期限状态文本
    getDeadlineStatus(record) {
      const now = new Date()
      const deadlineDate = new Date(record.rectificationDeadline)
      const diffTime = deadlineDate.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays < 0
    },
    modalFormSubmit(data) {
      this.startProcess({ id: data })
    },
    startProcess: function (record) {
      var that = this
      var key = 'startProcess'
      this.$message.loading({ content: '正在提交流程...', key })
      var param = {
        flowCode: that.flowCode,
        id: record.id,
        formUrl: 'modules/quality/check/rectification/components/Form',
        formUrlMobile: 'qualityManagement/rectificationDetail',
      }
      postAction(that.url.startProcess, param).then((res) => {
        if (res.success) {
          that.$message.success({ content: res.message + '!', key, duration: 1 })
          that.modalFormOk()
        } else {
          that.$message.warning({ content: res.message, key, duration: 1 })
        }
      })
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>